import os
import sys
import json
import threading
import subprocess
import uuid
import time
from datetime import datetime
from pathlib import Path
from typing import Dict

# 设置UTF-8编码
if sys.platform.startswith('win'):
    # Windows系统设置UTF-8编码
    os.system('chcp 65001 >nul 2>&1')
    # 重新配置stdout和stderr使用UTF-8
    sys.stdout.reconfigure(encoding='utf-8')
    sys.stderr.reconfigure(encoding='utf-8')

from flask import Flask, render_template, request, jsonify
from flask_socketio import SocketIO, emit

from dotenv import load_dotenv
load_dotenv()

# 测试模式配置
DEBUG_MODE = False  # 设置为 True 启用测试模式
DEBUG_SESSION_ID = "07200dd0-cec7-4b06-826b-4384bcfe6f27"  # 测试用的会话ID

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

def format_message_for_display(message_info: Dict) -> Dict:
    """格式化消息用于显示"""
    name = message_info.get('name', 'Unknown')
    content = message_info.get('content', '')
    timestamp = message_info.get('timestamp', '')
    role = message_info.get('role', 'assistant')
    raw_content = message_info.get('raw_content', None)

    # 根据代理名称设置不同的图标
    icons = {
        'Morgan': '👔',  # CEO
        'Leo': '🔍',     # 信息检索专家
        'Ken': '📊',     # 题材挖掘专家
        'Lus': '💹',     # A股炒作大师
        'Jess': '📝',    # 秘书
        'System': '🤖'   # 系统
    }
    icon = icons.get(name, '🤖')

    formatted_msg = {
        'name': name,
        'content': content,
        'timestamp': timestamp,
        'role': role,
        'icon': icon
    }

    # 如果有raw_content，也包含进去
    if raw_content is not None:
        formatted_msg['raw_content'] = raw_content

    return formatted_msg

def process_agent_message(raw_content, agent_name):
    """处理代理消息，包括工具调用等复杂逻辑"""
    try:
        # 处理工具调用
        if isinstance(raw_content, list):
            text_content = ""

            for item in raw_content:
                if isinstance(item, dict):
                    item_type = item.get('type', '')

                    if item_type == 'text':
                        text_content += item.get('text', '')

                    elif item_type == 'tool_use':
                        tool_name = item.get('name', '未知工具')
                        tool_input = item.get('input', {})
                        tool_info = f"\n\n🔧 **工具调用**: {tool_name}\n📋 **参数**: `{json.dumps(tool_input, ensure_ascii=False)}`"
                        text_content += tool_info

                    elif item_type == 'tool_result':
                        tool_output = item.get('output', [])
                        tool_name = item.get('name', '工具')

                        print(f"处理工具结果: {tool_name}, 输出类型: {type(tool_output)}")

                        if tool_output:
                            try:
                                if isinstance(tool_output, list) and len(tool_output) > 0:
                                    first_output = tool_output[0]
                                    if isinstance(first_output, dict) and 'text' in first_output:
                                        output_text = first_output['text']
                                        print(f"工具输出文本长度: {len(output_text)}")

                                        try:
                                            # 尝试解析JSON数据
                                            parsed_data = json.loads(output_text)
                                            if isinstance(parsed_data, list) and len(parsed_data) > 0:
                                                result_info = f"\n\n✅ **工具执行完成**: {tool_name}\n📊 **获取数据**: {len(parsed_data)} 条记录"

                                                # 显示第一条记录的预览
                                                if isinstance(parsed_data[0], dict):
                                                    first_record = parsed_data[0]
                                                    if '标题' in first_record:
                                                        title = first_record['标题'][:100] + "..." if len(first_record['标题']) > 100 else first_record['标题']
                                                        result_info += f"\n📄 **数据预览**: {title}"
                                                    elif 'document' in first_record:
                                                        doc_preview = first_record['document'][:200] + "..." if len(first_record['document']) > 200 else first_record['document']
                                                        result_info += f"\n📄 **数据预览**: {doc_preview}"
                                            else:
                                                result_info = f"\n\n✅ **工具执行完成**: {tool_name}\n📊 **返回结果**: 数据已获取"
                                        except json.JSONDecodeError:
                                            # 如果不是JSON，显示文本预览
                                            preview = output_text[:300] + "..." if len(output_text) > 300 else output_text
                                            result_info = f"\n\n✅ **工具执行完成**: {tool_name}\n📊 **返回结果**: {preview}"
                                    else:
                                        result_info = f"\n\n✅ **工具执行完成**: {tool_name}\n📊 **返回结果**: 数据已获取"
                                else:
                                    result_info = f"\n\n✅ **工具执行完成**: {tool_name}\n📊 **返回结果**: 执行完成"
                            except Exception as e:
                                print(f"处理工具结果时出错: {e}")
                                result_info = f"\n\n✅ **工具执行完成**: {tool_name}\n📊 **返回结果**: 数据已获取"
                        else:
                            result_info = f"\n\n✅ **工具执行完成**: {tool_name}"

                        text_content += result_info

                elif hasattr(item, 'text'):
                    text_content += item.text
                else:
                    text_content += str(item)

            return text_content
        else:
            return str(raw_content)

    except Exception as e:
        print(f"处理代理消息错误: {e}")
        import traceback
        traceback.print_exc()
        return str(raw_content)

def should_merge_tool_result(message_info, last_sent_message):
    """判断是否应该合并tool_result消息 - 简化版本"""
    # 简单判断：如果是system角色且内容为空，就尝试合并
    if message_info.get('role') == 'system' and not message_info.get('content', '').strip():
        # 检查是否有上一条消息
        if last_sent_message:
            print(f"🔍 检测到system空消息，尝试合并到上一条消息")
            return True

    return False

def merge_tool_result_to_last_message(tool_result_message, last_sent_message):
    """将tool_result合并到上一条消息并更新前端 - 简化版本"""
    try:
        # 处理tool_result内容
        processed_content = process_agent_message(
            tool_result_message.get('raw_content', []),
            tool_result_message.get('name', 'system')
        )

        print(f"📝 处理后的工具结果内容: {processed_content[:100]}...")

        if processed_content.strip():
            # 将工具结果添加到上一条消息的内容中
            current_content = last_sent_message.get('content', '')
            updated_content = current_content + '\n\n' + processed_content

            print(f"🔄 更新消息内容，原长度: {len(current_content)}, 新长度: {len(updated_content)}")

            # 发送更新消息到前端
            update_message = {
                'content': updated_content,
                'raw_content': tool_result_message.get('raw_content', [])  # 包含原始内容
            }
            socketio.emit('update_message', update_message)

            # 更新记录的最后消息内容
            last_sent_message['content'] = updated_content

            print(f"✅ 合并完成，已发送更新到前端")
        else:
            print(f"⚠️ 处理后的内容为空，跳过合并")

    except Exception as e:
        print(f"❌ 合并过程中出错: {e}")
        import traceback
        traceback.print_exc()

def load_debug_messages():
    """加载测试模式的消息"""
    debug_file = Path("messages") / f"session_{DEBUG_SESSION_ID}.jsonl"

    if not debug_file.exists():
        print(f"❌ 测试消息文件不存在: {debug_file}")
        return

    print(f"📂 加载测试消息文件: {debug_file}")

    try:
        messages = []
        last_sent_message = None

        # 先读取所有消息
        with open(debug_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue

                try:
                    message_info = json.loads(line)
                    messages.append((line_num, message_info))
                except json.JSONDecodeError as e:
                    print(f"❌ 解析消息失败 (行 {line_num}): {e}")
                    continue

        # 处理消息，实现合并逻辑
        for line_num, message_info in messages:
            # 检查是否应该合并tool_result
            if should_merge_tool_result(message_info, last_sent_message):
                print(f"🔗 合并tool_result到上一条消息 (行 {line_num})")
                merge_tool_result_to_last_message(message_info, last_sent_message)
                time.sleep(0.3)  # 短暂延迟
                continue

            # 处理原始内容
            if 'raw_content' in message_info:
                processed_content = process_agent_message(
                    message_info['raw_content'],
                    message_info['name']
                )
                message_info['content'] = processed_content

            # 格式化并发送到前端
            formatted_msg = format_message_for_display(message_info)
            socketio.emit('agent_message', formatted_msg)
            last_sent_message = formatted_msg  # 记录最后发送的消息

            print(f"📤 发送测试消息 {line_num}: {message_info['name']} - {message_info['content'][:50]}...")

            # 短暂延迟，模拟真实消息流
            time.sleep(0.5)

    except Exception as e:
        print(f"❌ 加载测试消息时出错: {e}")
        import traceback
        traceback.print_exc()

def monitor_message_file(session_id):
    """监控消息文件"""
    message_file = Path("messages") / f"session_{session_id}.jsonl"

    print(f"开始监控消息文件: {message_file}")

    # 记录已处理的消息和最后一条发送的消息
    processed_messages = set()
    last_file_size = 0
    last_sent_message = None  # 记录最后一条发送到前端的消息

    # 等待文件创建
    max_wait = 30  # 最多等待30秒
    wait_count = 0
    while not message_file.exists() and wait_count < max_wait:
        time.sleep(1)
        wait_count += 1

    if not message_file.exists():
        print(f"消息文件创建超时: {message_file}")
        return

    print(f"消息文件已创建，开始监控: {message_file}")

    while True:
        try:
            # 检查文件大小是否有变化
            current_size = message_file.stat().st_size
            if current_size == last_file_size:
                time.sleep(0.1)
                continue

            last_file_size = current_size

            # 读取文件中的所有消息
            with open(message_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()

            print(f"文件更新，读取到 {len(lines)} 行")

            # 处理新消息
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line:
                    continue

                try:
                    message_info = json.loads(line)
                    message_id = message_info.get('message_id', f'line_{line_num}')

                    # 跳过已处理的消息
                    if message_id in processed_messages:
                        continue

                    processed_messages.add(message_id)
                    print(f"处理新消息: {message_info['name']} - {message_id}")

                    # 检查是否是tool_result消息，如果是则尝试合并
                    if should_merge_tool_result(message_info, last_sent_message):
                        print(f"🔗 合并tool_result到上一条消息")
                        merge_tool_result_to_last_message(message_info, last_sent_message)
                        continue  # 不发送新消息，已经合并了

                    # 处理原始内容
                    if 'raw_content' in message_info:
                        processed_content = process_agent_message(
                            message_info['raw_content'],
                            message_info['name']
                        )
                        message_info['content'] = processed_content

                    # 格式化并发送到前端
                    formatted_msg = format_message_for_display(message_info)
                    socketio.emit('agent_message', formatted_msg)
                    last_sent_message = formatted_msg  # 记录最后发送的消息

                    print(f"✅ 发送消息到前端: {message_info['name']} - {message_info['content'][:50]}...")

                    # 检查是否是完成或错误信号
                    if message_info.get('type') in ['completion', 'error']:
                        print(f"收到结束信号: {message_info.get('type')}")
                        return

                except json.JSONDecodeError as e:
                    print(f"解析消息JSON失败 (行 {line_num}): {e}")
                    print(f"问题行内容: {line[:100]}...")
                    continue
                except Exception as e:
                    print(f"处理消息时出错 (行 {line_num}): {e}")
                    continue

        except Exception as e:
            print(f"监控消息文件时出错: {e}")
            import traceback
            traceback.print_exc()
            time.sleep(1)  # 出错后等待1秒再重试

@app.route('/')
def index():
    """主页面"""
    return render_template('index.html')

@app.route('/test_collapsible.html')
def test_collapsible():
    """工具折叠测试页面"""
    return render_template('test_collapsible.html')

@app.route('/api/init', methods=['POST'])
def api_init():
    """初始化系统 - 保留向后兼容性"""
    try:
        # 创建消息目录
        message_dir = Path("messages")
        message_dir.mkdir(exist_ok=True)
        
        return jsonify({'status': 'success', 'message': '系统已就绪，您可以开始咨询股票相关问题。'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': f'初始化错误: {str(e)}'})

@app.route('/api/session/<session_id>/messages', methods=['GET'])
def get_session_messages(session_id):
    """获取指定会话的消息"""
    try:
        message_file = Path("messages") / f"session_{session_id}.jsonl"
        
        if not message_file.exists():
            return jsonify([]), 200  # 返回空数组而不是错误
        
        messages = []
        last_sent_message = None
        
        with open(message_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue
                
                try:
                    message_info = json.loads(line)
                    
                    # 检查是否应该合并tool_result
                    if should_merge_tool_result(message_info, last_sent_message):
                        # 处理tool_result内容并合并到最后一条消息
                        processed_content = process_agent_message(
                            message_info.get('raw_content', []),
                            message_info.get('name', 'system')
                        )
                        
                        if processed_content.strip() and last_sent_message:
                            # 将工具结果添加到上一条消息的内容中
                            current_content = last_sent_message.get('content', '')
                            updated_content = current_content + '\n\n' + processed_content
                            last_sent_message['content'] = updated_content
                            # 更新messages列表中的最后一条消息
                            if messages:
                                messages[-1] = last_sent_message
                        continue
                    
                    # 处理原始内容
                    if 'raw_content' in message_info:
                        processed_content = process_agent_message(
                            message_info['raw_content'],
                            message_info['name']
                        )
                        message_info['content'] = processed_content
                    
                    # 格式化消息
                    formatted_msg = format_message_for_display(message_info)
                    messages.append(formatted_msg)
                    last_sent_message = formatted_msg
                    
                except json.JSONDecodeError:
                    continue
        
        return jsonify(messages)
        
    except Exception as e:
        print(f"获取会话消息错误: {e}")
        return jsonify({'error': str(e)}), 500

@socketio.on('connect')
def handle_connect():
    """WebSocket连接处理"""
    print('Client connected')
    emit('status', {'message': '连接成功，您可以开始咨询'})

@socketio.on('disconnect')
def handle_disconnect():
    """WebSocket断开连接处理"""
    print('Client disconnected')

@socketio.on('send_message')
def handle_message(data):
    """处理用户消息"""
    message = data.get('message', '')
    if not message.strip():
        emit('error', {'message': '消息不能为空'})
        return
    
    try:
        if DEBUG_MODE:
            # 测试模式：直接加载预设的消息文件
            print(f"🧪 测试模式：加载预设消息文件")

            # 发送开始处理的消息
            emit('agent_message', {
                'name': 'System',
                'content': f'🧪 测试模式：加载会话 {DEBUG_SESSION_ID} 的消息...',
                'timestamp': datetime.now().strftime("%H:%M:%S"),
                'role': 'system',
                'icon': '🤖'
            })

            # 在后台线程中加载测试消息
            def load_test_messages():
                time.sleep(1)  # 短暂延迟
                load_debug_messages()

                # 发送完成消息
                socketio.emit('agent_message', {
                    'name': 'System',
                    'content': '✅ 测试消息加载完成',
                    'timestamp': datetime.now().strftime("%H:%M:%S"),
                    'role': 'system',
                    'icon': '🤖'
                })

            test_thread = threading.Thread(target=load_test_messages, daemon=True)
            test_thread.start()

        else:
            # 正常模式：启动代理进程
            # 生成会话ID
            session_id = str(uuid.uuid4())

            # 发送会话ID给前端
            emit('session_created', {'session_id': session_id})

            # 发送开始处理的消息
            emit('agent_message', {
                'name': 'System',
                'content': '🚀 开始分析，正在启动专家团队...',
                'timestamp': datetime.now().strftime("%H:%M:%S"),
                'role': 'system',
                'icon': '🤖'
            })

            # 启动消息文件监控线程
            monitor_thread = threading.Thread(
                target=monitor_message_file,
                args=(session_id,),
                daemon=True
            )
            monitor_thread.start()

            # 启动独立进程处理代理
            python_path = "python"  # 或者使用 sys.executable
            agent_script = "agent_v3.py"

            process = subprocess.Popen(
                [python_path, agent_script, message, session_id],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                errors='ignore'  # 忽略编码错误
            )

            print(f"启动代理进程，PID: {process.pid}, 会话ID: {session_id}")

            # 在后台等待进程完成
            def wait_for_process():
                try:
                    _, stderr = process.communicate(timeout=900)  # 15分钟超时
                    print(f"代理进程完成，返回码: {process.returncode}")
                    if stderr:
                        print(f"代理进程错误输出: {stderr}")
                except subprocess.TimeoutExpired:
                    print("代理进程超时，终止进程")
                    process.kill()
                    emit('error', {'message': '⏰ 处理超时，请重试'})
                except Exception as e:
                    print(f"等待代理进程时出错: {e}")
                    emit('error', {'message': f'❌ 处理错误: {str(e)}'})

            wait_thread = threading.Thread(target=wait_for_process, daemon=True)
            wait_thread.start()
        
    except Exception as e:
        error_msg = f"❌ 系统错误: {str(e)}"
        print(f"处理消息错误: {e}")
        import traceback
        traceback.print_exc()
        emit('error', {'message': error_msg})

if __name__ == '__main__':
    # 创建必要的目录
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static/css', exist_ok=True)
    os.makedirs('static/js', exist_ok=True)
    os.makedirs('messages', exist_ok=True)
    
    if DEBUG_MODE:
        print("🧪 启动股票咨询代理Web系统 (测试模式)...")
        print("📝 测试模式说明：")
        print("   1. 打开浏览器访问 http://localhost:5000")
        print("   2. 在聊天界面输入任意消息")
        print(f"   3. 系统将加载预设的测试消息文件: session_{DEBUG_SESSION_ID}.jsonl")
        print("   4. 可以测试前端显示效果，无需启动代理进程")
        print("   5. 支持历史对话记录和会话管理")
        print(f"   6. 要切换到正常模式，请将 DEBUG_MODE 设置为 False")
    else:
        print("🚀 启动股票咨询代理Web系统 (文件通信版本)...")
        print("📝 使用说明：")
        print("   1. 打开浏览器访问 http://localhost:5000")
        print("   2. 在聊天界面输入您想分析的题材")
        print("   3. 系统将启动独立进程协调多个专家为您提供分析")
        print("   4. 消息通过文件系统进行进程间通信")
        print("   5. 支持历史对话记录和会话管理")
    
    socketio.run(app, host='0.0.0.0', port=5000, debug=False)
